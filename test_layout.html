<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Box Layout Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .layout-section {
            margin-bottom: 40px;
        }

        .layout-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #555;
        }

        /* Original Layout (Vertical) */
        .original-layout {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }

        .original-chat-input-container {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: stretch;
            height: 80px;
            max-width: 600px;
            width: 80%;
            padding: 0 10px 8px 10px;
            border-radius: 8px;
            margin: 16px auto 0 auto;
            background: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.06);
            border: 1px solid #bdbdbd;
        }

        .original-chat-input-form {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: stretch;
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            box-shadow: none;
            height: 100%;
        }

        .original-input-main-area {
            width: 100%;
            flex: 1 1 auto;
            display: block;
        }

        .original-input-main-area textarea {
            width: 100%;
            font-size: 1rem;
            padding: 8px 12px;
            border-radius: 12px 12px 0 0;
            border: none;
            outline: none;
            background: transparent;
            resize: none;
            min-height: 36px;
            max-height: 120px;
            box-sizing: border-box;
        }

        .original-input-actions-row {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            justify-content: space-between;
            padding: 0;
            margin: 0;
            gap: 0;
        }

        /* New Layout (Horizontal) */
        .new-layout {
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            background: #f8fff8;
        }

        .new-chat-input-container {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 48px;
            min-height: 48px;
            max-width: 600px;
            width: 80%;
            padding: 0 4px;
            border-radius: 8px;
            margin: 16px auto 0 auto;
            background: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.06);
            border: 1px solid #bdbdbd;
        }

        .new-chat-input-form {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            box-shadow: none;
            height: 100%;
        }

        .input-horizontal-row {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            padding: 8px 12px;
        }

        .input-horizontal-row .left-actions {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-shrink: 0;
        }

        .input-horizontal-row .input-main-area {
            flex: 1;
            display: flex;
            align-items: center;
            margin: 0;
        }

        .input-horizontal-row .input-main-area textarea {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            resize: none;
            font-size: 1rem;
            padding: 8px 12px;
            min-height: 20px;
            max-height: 80px;
            line-height: 1.4;
        }

        .input-horizontal-row .right-actions {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        /* Common button styles */
        .action-btn {
            width: 22px;
            height: 22px;
            min-width: 22px;
            min-height: 22px;
            padding: 0;
            margin: 0 4px 0 0;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background: none;
            border: none;
            box-shadow: none;
            transition: background 0.2s;
            cursor: pointer;
            color: #666;
        }

        .action-btn:hover {
            background: #f0f0f0;
        }

        .send-btn {
            width: 32px;
            height: 32px;
            min-width: 32px;
            min-height: 32px;
            margin: 0 0 0 8px;
            background: #333;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-btn:hover {
            background: #555;
        }

        .left-actions, .right-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Message Box Layout Comparison</h1>
        
        <div class="layout-section">
            <div class="layout-title">Original Layout (Vertical)</div>
            <div class="original-layout">
                <div class="original-chat-input-container">
                    <form class="original-chat-input-form">
                        <!-- Main input area -->
                        <div class="original-input-main-area">
                            <textarea placeholder="💬 How can I help you today?" rows="1"></textarea>
                        </div>

                        <!-- Action buttons row -->
                        <div class="original-input-actions-row">
                            <div class="left-actions">
                                <button type="button" class="action-btn" title="Upload Documents">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button type="button" class="action-btn" title="Voice Input">
                                    <i class="fas fa-microphone"></i>
                                </button>
                            </div>
                            <div class="right-actions">
                                <button type="submit" class="send-btn" title="Send message">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="layout-section">
            <div class="layout-title">New Layout (Horizontal)</div>
            <div class="new-layout">
                <div class="new-chat-input-container">
                    <form class="new-chat-input-form">
                        <!-- Single row layout: left actions, textarea, right actions -->
                        <div class="input-horizontal-row">
                            <div class="left-actions">
                                <button type="button" class="action-btn" title="Upload Documents">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button type="button" class="action-btn" title="Voice Input">
                                    <i class="fas fa-microphone"></i>
                                </button>
                            </div>
                            
                            <!-- Main input area -->
                            <div class="input-main-area">
                                <textarea placeholder="💬 How can I help you today?" rows="1"></textarea>
                            </div>
                            
                            <div class="right-actions">
                                <button type="submit" class="send-btn" title="Send message">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
