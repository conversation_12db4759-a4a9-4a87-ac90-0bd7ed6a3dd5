<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Message Box Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        /* Exact layout from your image */
        .message-box {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #bdbdbd;
            border-radius: 8px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.06);
            max-width: 600px;
            margin: 0 auto;
        }

        .left-actions {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-shrink: 0;
        }

        .action-btn {
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: none;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #f0f0f0;
        }

        .input-area {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .input-area textarea {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            resize: none;
            font-size: 1rem;
            padding: 8px 12px;
            min-height: 20px;
            max-height: 80px;
            line-height: 1.4;
            font-family: inherit;
        }

        .right-actions {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .send-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #333;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-btn:hover {
            background: #555;
        }

        .description {
            text-align: center;
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Your Requested Message Box Layout</h1>
        
        <!-- This is exactly what you asked for -->
        <div class="message-box">
            <div class="left-actions">
                <button class="action-btn" title="Attach file">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="action-btn" title="Voice input">
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
            
            <div class="input-area">
                <textarea placeholder="💬 How can I help you today?" rows="1"></textarea>
            </div>
            
            <div class="right-actions">
                <button class="send-btn" title="Send">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
        
        <div class="description">
            <p><strong>Layout:</strong> 📎 🎤 [💬 How can I help you today?] ▶</p>
            <p>This matches exactly what you showed in your image!</p>
        </div>
    </div>
</body>
</html>
