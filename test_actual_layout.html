<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Actual Message Box Layout Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/chatgpt-style-clean.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .test-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .demo-area {
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            background: #f8fff8;
            position: relative;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Override any positioning for demo */
        .demo-area .chat-input-container {
            position: relative !important;
            bottom: auto !important;
            left: auto !important;
            right: auto !important;
            transform: none !important;
            margin: 0 !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Actual Message Box Layout Test</h1>
        
        <div class="demo-area">
            <!-- This is the exact same structure as in your templates/index.html -->
            <footer class="chat-input-container">
                <form id="chatForm" class="chat-input-form">
                    <!-- Single row layout: left actions, textarea, right actions -->
                    <div class="input-horizontal-row">
                        <div class="left-actions">
                            <button type="button" id="quickUploadBtn" class="action-btn" title="Upload Documents">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <input type="file" id="quickFileUpload" accept=".pdf,.docx,.txt,.md" hidden multiple>
                            <button type="button" id="voiceInputBtn" class="action-btn" title="Voice Input">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>
                        
                        <!-- Main input area -->
                        <div class="input-main-area">
                            <textarea id="userInput" placeholder="💬 How can I help you today?" rows="1" autocomplete="off"></textarea>
                        </div>
                        
                        <div class="right-actions">
                            <button type="submit" id="sendBtn" class="send-btn" title="Send message">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </footer>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #666;">
            <p>This uses the actual CSS from your project to test the horizontal layout.</p>
            <p>Layout should show: 📎 🎤 [text input] ▶</p>
        </div>
    </div>
</body>
</html>
